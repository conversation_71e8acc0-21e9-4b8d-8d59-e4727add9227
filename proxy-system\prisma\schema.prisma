// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// 用户模型
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  username      String?   @unique
  password      String?
  name          String?
  avatar        String?
  role          Role      @default(USER)
  balance       Float     @default(0)
  isActive      Boolean   @default(true)
  emailVerified DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // 关联
  accounts      Account[]
  sessions      Session[]
  orders        Order[]
  proxies       Proxy[]
  transactions  Transaction[]

  @@map("users")
}

// NextAuth账户模型
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

// NextAuth会话模型
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// 验证令牌模型
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// 代理模型
model Proxy {
  id          String      @id @default(cuid())
  px6Id       String      @unique // px6.me系统中的代理ID
  ip          String
  host        String
  port        Int
  username    String
  password    String
  type        ProxyType   @default(HTTP)
  country     String
  version     ProxyVersion @default(IPV6)
  status      ProxyStatus @default(ACTIVE)
  description String?

  // 时间信息
  purchaseDate DateTime
  expiryDate   DateTime
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // 关联
  userId       String
  user         User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  orderId      String
  order        Order       @relation(fields: [orderId], references: [id])

  @@map("proxies")
}

// 订单模型
model Order {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  status      OrderStatus @default(PENDING)

  // 订单详情
  count       Int
  period      Int         // 天数
  country     String
  version     ProxyVersion @default(IPV6)
  type        ProxyType   @default(HTTP)
  description String?

  // 价格信息
  unitPrice   Float
  totalPrice  Float

  // 时间信息
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  paidAt      DateTime?

  // 关联
  userId      String
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  proxies     Proxy[]
  transaction Transaction?

  @@map("orders")
}

// 交易记录模型
model Transaction {
  id            String          @id @default(cuid())
  type          TransactionType
  amount        Float
  status        TransactionStatus @default(PENDING)
  description   String?
  paymentMethod String?

  // 外部支付信息
  externalId    String?         @unique
  paymentData   String?

  // 时间信息
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  completedAt   DateTime?

  // 关联
  userId        String
  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  orderId       String?         @unique
  order         Order?          @relation(fields: [orderId], references: [id])

  @@map("transactions")
}

// 系统配置模型
model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("system_configs")
}

// 枚举类型
enum Role {
  USER
  ADMIN
  SUPER_ADMIN
}

enum ProxyType {
  HTTP
  SOCKS
}

enum ProxyVersion {
  IPV4
  IPV4_SHARED
  IPV6
}

enum ProxyStatus {
  ACTIVE
  EXPIRED
  EXPIRING
  DELETED
}

enum OrderStatus {
  PENDING
  PAID
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum TransactionType {
  RECHARGE      // 充值
  PURCHASE      // 购买代理
  REFUND        // 退款
  COMMISSION    // 佣金
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}
