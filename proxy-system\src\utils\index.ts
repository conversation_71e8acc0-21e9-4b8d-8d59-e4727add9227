import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

// 合并Tailwind CSS类名
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// 格式化货币
export function formatCurrency(amount: number, currency: string = 'CNY'): string {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
  }).format(amount)
}

// 格式化日期
export function formatDate(date: Date | string, options?: Intl.DateTimeFormatOptions): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    ...options,
  }).format(dateObj)
}

// 格式化相对时间
export function formatRelativeTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes}分钟前`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours}小时前`
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days}天前`
  } else {
    return formatDate(dateObj, { month: 'short', day: 'numeric' })
  }
}

// 生成随机字符串
export function generateRandomString(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 生成订单号
export function generateOrderNumber(): string {
  const timestamp = Date.now().toString()
  const random = generateRandomString(4)
  return `ORD${timestamp}${random}`
}

// 验证邮箱格式
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证密码强度
export function validatePassword(password: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  if (password.length < 8) {
    errors.push('密码长度至少8位')
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('密码必须包含至少一个大写字母')
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('密码必须包含至少一个小写字母')
  }
  
  if (!/\d/.test(password)) {
    errors.push('密码必须包含至少一个数字')
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  }
}

// 计算代理到期状态
export function getProxyExpiryStatus(expiryDate: Date): {
  status: 'active' | 'expiring' | 'expired'
  daysLeft: number
} {
  const now = new Date()
  const expiry = new Date(expiryDate)
  const diffInMs = expiry.getTime() - now.getTime()
  const daysLeft = Math.ceil(diffInMs / (1000 * 60 * 60 * 24))

  if (daysLeft < 0) {
    return { status: 'expired', daysLeft: 0 }
  } else if (daysLeft <= 7) {
    return { status: 'expiring', daysLeft }
  } else {
    return { status: 'active', daysLeft }
  }
}

// 获取国家名称
export function getCountryName(countryCode: string): string {
  const countryNames: Record<string, string> = {
    'ru': '俄罗斯',
    'us': '美国',
    'ua': '乌克兰',
    'de': '德国',
    'fr': '法国',
    'gb': '英国',
    'jp': '日本',
    'kr': '韩国',
    'cn': '中国',
    'hk': '香港',
    'sg': '新加坡',
    'ca': '加拿大',
    'au': '澳大利亚',
    'br': '巴西',
    'in': '印度',
    'it': '意大利',
    'es': '西班牙',
    'nl': '荷兰',
    'se': '瑞典',
    'no': '挪威',
    'fi': '芬兰',
    'dk': '丹麦',
    'pl': '波兰',
    'cz': '捷克',
    'at': '奥地利',
    'ch': '瑞士',
    'be': '比利时',
    'ie': '爱尔兰',
    'pt': '葡萄牙',
    'gr': '希腊',
    'tr': '土耳其',
    'il': '以色列',
    'ae': '阿联酋',
    'sa': '沙特阿拉伯',
    'eg': '埃及',
    'za': '南非',
    'mx': '墨西哥',
    'ar': '阿根廷',
    'cl': '智利',
    'co': '哥伦比亚',
    'pe': '秘鲁',
    'th': '泰国',
    'vn': '越南',
    'my': '马来西亚',
    'id': '印度尼西亚',
    'ph': '菲律宾',
    'nz': '新西兰',
  }
  
  return countryNames[countryCode.toLowerCase()] || countryCode.toUpperCase()
}

// 获取代理类型显示名称
export function getProxyTypeDisplayName(type: string): string {
  const typeNames: Record<string, string> = {
    'HTTP': 'HTTP/HTTPS',
    'SOCKS': 'SOCKS5',
  }
  return typeNames[type] || type
}

// 获取代理版本显示名称
export function getProxyVersionDisplayName(version: string): string {
  const versionNames: Record<string, string> = {
    'IPV4': 'IPv4',
    'IPV4_SHARED': 'IPv4 共享',
    'IPV6': 'IPv6',
  }
  return versionNames[version] || version
}

// 获取订单状态显示名称和颜色
export function getOrderStatusDisplay(status: string): {
  name: string
  color: string
} {
  const statusMap: Record<string, { name: string; color: string }> = {
    'PENDING': { name: '待支付', color: 'yellow' },
    'PAID': { name: '已支付', color: 'blue' },
    'PROCESSING': { name: '处理中', color: 'purple' },
    'COMPLETED': { name: '已完成', color: 'green' },
    'FAILED': { name: '失败', color: 'red' },
    'CANCELLED': { name: '已取消', color: 'gray' },
    'REFUNDED': { name: '已退款', color: 'orange' },
  }
  
  return statusMap[status] || { name: status, color: 'gray' }
}

// 获取交易状态显示名称和颜色
export function getTransactionStatusDisplay(status: string): {
  name: string
  color: string
} {
  const statusMap: Record<string, { name: string; color: string }> = {
    'PENDING': { name: '待处理', color: 'yellow' },
    'COMPLETED': { name: '已完成', color: 'green' },
    'FAILED': { name: '失败', color: 'red' },
    'CANCELLED': { name: '已取消', color: 'gray' },
  }
  
  return statusMap[status] || { name: status, color: 'gray' }
}

// 分页计算
export function calculatePagination(total: number, page: number, limit: number) {
  const totalPages = Math.ceil(total / limit)
  const hasNext = page < totalPages
  const hasPrev = page > 1
  
  return {
    totalPages,
    hasNext,
    hasPrev,
    startIndex: (page - 1) * limit,
    endIndex: Math.min(page * limit, total),
  }
}
