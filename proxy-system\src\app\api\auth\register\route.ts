import { NextRequest } from 'next/server'
import bcrypt from 'bcryptjs'
import { databaseService } from '@/services/database'
import { createSuccessResponse, createErrorResponse, ApiErrors } from '@/lib/api-response'
import { isValidEmail, validatePassword } from '@/utils'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password, name, username } = body

    // 验证必填字段
    if (!email || !password) {
      return createErrorResponse('邮箱和密码为必填项', 400)
    }

    // 验证邮箱格式
    if (!isValidEmail(email)) {
      return createErrorResponse('邮箱格式不正确', 400)
    }

    // 验证密码强度
    const passwordValidation = validatePassword(password)
    if (!passwordValidation.isValid) {
      return createErrorResponse(passwordValidation.errors.join(', '), 400)
    }

    // 检查邮箱是否已存在
    const existingUser = await databaseService.getUserByEmail(email)
    if (existingUser) {
      return createErrorResponse('该邮箱已被注册', 400)
    }

    // 检查用户名是否已存在（如果提供了用户名）
    if (username) {
      const existingUsername = await databaseService.getUserByEmail(username)
      if (existingUsername) {
        return createErrorResponse('该用户名已被使用', 400)
      }
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12)

    // 创建用户
    const user = await databaseService.createUser({
      email,
      password: hashedPassword,
      name,
      username,
    })

    // 返回用户信息（不包含密码）
    const { password: _, ...userWithoutPassword } = user

    return createSuccessResponse(userWithoutPassword, '注册成功')
  } catch (error) {
    console.error('Registration error:', error)
    return createErrorResponse('注册失败，请稍后重试', 500)
  }
}
