import { prisma } from '@/lib/prisma'
import { 
  User, 
  Order, 
  Proxy, 
  Transaction, 
  OrderStatus, 
  TransactionType, 
  TransactionStatus,
  ProxyStatus,
  ProxyType,
  ProxyVersion
} from '@prisma/client'
import { 
  CreateOrderData, 
  PaginationParams, 
  PaginatedResponse,
  ProxyFilters,
  OrderFilters,
  TransactionFilters
} from '@/types'
import { generateOrderNumber } from '@/utils'

export class DatabaseService {
  // 用户相关操作
  async getUserById(id: string) {
    return prisma.user.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            orders: true,
            proxies: true,
            transactions: true,
          }
        }
      }
    })
  }

  async getUserByEmail(email: string) {
    return prisma.user.findUnique({
      where: { email }
    })
  }

  async createUser(data: {
    email: string
    password: string
    name?: string
    username?: string
  }) {
    return prisma.user.create({
      data
    })
  }

  async updateUserBalance(userId: string, amount: number) {
    return prisma.user.update({
      where: { id: userId },
      data: {
        balance: {
          increment: amount
        }
      }
    })
  }

  // 订单相关操作
  async createOrder(userId: string, orderData: CreateOrderData) {
    const orderNumber = generateOrderNumber()
    
    return prisma.order.create({
      data: {
        orderNumber,
        userId,
        status: OrderStatus.PENDING,
        count: orderData.count,
        period: orderData.period,
        country: orderData.country,
        version: orderData.version,
        type: orderData.type,
        description: orderData.description,
        unitPrice: 0, // 将在计算价格后更新
        totalPrice: 0, // 将在计算价格后更新
      }
    })
  }

  async updateOrderPrice(orderId: string, unitPrice: number, totalPrice: number) {
    return prisma.order.update({
      where: { id: orderId },
      data: {
        unitPrice,
        totalPrice,
      }
    })
  }

  async updateOrderStatus(orderId: string, status: OrderStatus, paidAt?: Date) {
    return prisma.order.update({
      where: { id: orderId },
      data: {
        status,
        paidAt,
      }
    })
  }

  async getOrderById(id: string) {
    return prisma.order.findUnique({
      where: { id },
      include: {
        user: true,
        proxies: true,
        transaction: true,
      }
    })
  }

  async getOrdersByUser(
    userId: string, 
    pagination: PaginationParams,
    filters?: OrderFilters
  ): Promise<PaginatedResponse<Order>> {
    const where: any = { userId }
    
    if (filters?.status) {
      where.status = filters.status
    }
    
    if (filters?.dateFrom || filters?.dateTo) {
      where.createdAt = {}
      if (filters.dateFrom) {
        where.createdAt.gte = filters.dateFrom
      }
      if (filters.dateTo) {
        where.createdAt.lte = filters.dateTo
      }
    }

    if (filters?.search) {
      where.OR = [
        { orderNumber: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
      ]
    }

    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        skip: (pagination.page - 1) * pagination.limit,
        take: pagination.limit,
        orderBy: { createdAt: 'desc' },
        include: {
          proxies: true,
        }
      }),
      prisma.order.count({ where })
    ])

    return {
      data: orders,
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages: Math.ceil(total / pagination.limit),
    }
  }

  // 代理相关操作
  async createProxies(orderId: string, proxiesData: Array<{
    px6Id: string
    ip: string
    host: string
    port: number
    username: string
    password: string
    type: ProxyType
    country: string
    version: ProxyVersion
    purchaseDate: Date
    expiryDate: Date
    description?: string
  }>) {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      select: { userId: true }
    })

    if (!order) {
      throw new Error('Order not found')
    }

    return prisma.proxy.createMany({
      data: proxiesData.map(proxy => ({
        ...proxy,
        userId: order.userId,
        orderId,
        status: ProxyStatus.ACTIVE,
      }))
    })
  }

  async getProxiesByUser(
    userId: string,
    pagination: PaginationParams,
    filters?: ProxyFilters
  ): Promise<PaginatedResponse<Proxy>> {
    const where: any = { userId }
    
    if (filters?.status) {
      where.status = filters.status
    }
    
    if (filters?.country) {
      where.country = filters.country
    }
    
    if (filters?.type) {
      where.type = filters.type
    }
    
    if (filters?.version) {
      where.version = filters.version
    }

    if (filters?.search) {
      where.OR = [
        { ip: { contains: filters.search, mode: 'insensitive' } },
        { host: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
      ]
    }

    const [proxies, total] = await Promise.all([
      prisma.proxy.findMany({
        where,
        skip: (pagination.page - 1) * pagination.limit,
        take: pagination.limit,
        orderBy: { createdAt: 'desc' },
        include: {
          order: true,
        }
      }),
      prisma.proxy.count({ where })
    ])

    return {
      data: proxies,
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages: Math.ceil(total / pagination.limit),
    }
  }

  async updateProxyStatus(proxyId: string, status: ProxyStatus) {
    return prisma.proxy.update({
      where: { id: proxyId },
      data: { status }
    })
  }

  async updateProxyExpiry(proxyId: string, expiryDate: Date) {
    return prisma.proxy.update({
      where: { id: proxyId },
      data: { expiryDate }
    })
  }

  // 交易相关操作
  async createTransaction(data: {
    userId: string
    type: TransactionType
    amount: number
    description?: string
    orderId?: string
    paymentMethod?: string
  }) {
    return prisma.transaction.create({
      data: {
        ...data,
        status: TransactionStatus.PENDING,
      }
    })
  }

  async updateTransactionStatus(
    transactionId: string, 
    status: TransactionStatus,
    externalId?: string,
    paymentData?: any,
    completedAt?: Date
  ) {
    return prisma.transaction.update({
      where: { id: transactionId },
      data: {
        status,
        externalId,
        paymentData,
        completedAt,
      }
    })
  }

  async getTransactionsByUser(
    userId: string,
    pagination: PaginationParams,
    filters?: TransactionFilters
  ): Promise<PaginatedResponse<Transaction>> {
    const where: any = { userId }
    
    if (filters?.type) {
      where.type = filters.type
    }
    
    if (filters?.status) {
      where.status = filters.status
    }
    
    if (filters?.dateFrom || filters?.dateTo) {
      where.createdAt = {}
      if (filters.dateFrom) {
        where.createdAt.gte = filters.dateFrom
      }
      if (filters.dateTo) {
        where.createdAt.lte = filters.dateTo
      }
    }

    if (filters?.search) {
      where.OR = [
        { description: { contains: filters.search, mode: 'insensitive' } },
        { paymentMethod: { contains: filters.search, mode: 'insensitive' } },
      ]
    }

    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where,
        skip: (pagination.page - 1) * pagination.limit,
        take: pagination.limit,
        orderBy: { createdAt: 'desc' },
        include: {
          order: true,
        }
      }),
      prisma.transaction.count({ where })
    ])

    return {
      data: transactions,
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages: Math.ceil(total / pagination.limit),
    }
  }

  // 统计相关操作
  async getDashboardStats(userId?: string) {
    const userFilter = userId ? { userId } : {}
    
    const [
      totalUsers,
      totalOrders,
      totalProxies,
      totalRevenue,
      activeProxies,
      expiringProxies,
      recentOrders,
      recentTransactions
    ] = await Promise.all([
      userId ? 1 : prisma.user.count(),
      prisma.order.count({ where: userFilter }),
      prisma.proxy.count({ where: userFilter }),
      prisma.transaction.aggregate({
        where: {
          ...userFilter,
          type: TransactionType.PURCHASE,
          status: TransactionStatus.COMPLETED,
        },
        _sum: { amount: true }
      }),
      prisma.proxy.count({
        where: {
          ...userFilter,
          status: ProxyStatus.ACTIVE,
        }
      }),
      prisma.proxy.count({
        where: {
          ...userFilter,
          status: ProxyStatus.EXPIRING,
        }
      }),
      prisma.order.findMany({
        where: userFilter,
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: { user: !userId }
      }),
      prisma.transaction.findMany({
        where: userFilter,
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: { user: !userId }
      })
    ])

    return {
      totalUsers,
      totalOrders,
      totalProxies,
      totalRevenue: totalRevenue._sum.amount || 0,
      activeProxies,
      expiringProxies,
      recentOrders,
      recentTransactions,
    }
  }
}

export const databaseService = new DatabaseService()
