import axios from 'axios'

const PX6_API_KEY = process.env.PX6_API_KEY
const PX6_API_BASE_URL = process.env.PX6_API_BASE_URL || 'https://px6.link/api'

if (!PX6_API_KEY) {
  throw new Error('PX6_API_KEY is not defined in environment variables')
}

// PX6 API响应类型
export interface PX6Response<T = any> {
  status: 'yes' | 'no'
  user_id?: string
  balance?: string
  currency?: string
  error_id?: number
  error?: string
  [key: string]: any
}

// 代理信息类型
export interface ProxyInfo {
  id: string
  ip: string
  host: string
  port: string
  user: string
  pass: string
  type: 'http' | 'socks'
  country: string
  date: string
  date_end: string
  unixtime: number
  unixtime_end: number
  descr: string
  active: '0' | '1'
}

// 价格信息类型
export interface PriceInfo {
  price: number
  price_single: number
  period: number
  count: number
}

class PX6Service {
  private baseURL: string

  constructor() {
    this.baseURL = `${PX6_API_BASE_URL}/${PX6_API_KEY}`
  }

  // 通用请求方法
  private async request<T>(method: string, params: Record<string, any> = {}): Promise<PX6Response<T>> {
    try {
      const url = `${this.baseURL}/${method}`
      const response = await axios.get(url, { params })
      return response.data
    } catch (error) {
      console.error('PX6 API Error:', error)
      throw new Error('PX6 API request failed')
    }
  }

  // 获取价格信息
  async getPrice(count: number, period: number, version: 4 | 3 | 6 = 6): Promise<PX6Response<PriceInfo>> {
    return this.request('getprice', { count, period, version })
  }

  // 获取可用代理数量
  async getCount(country: string, version: 4 | 3 | 6 = 6): Promise<PX6Response<{ count: number }>> {
    return this.request('getcount', { country, version })
  }

  // 获取可用国家列表
  async getCountries(version: 4 | 3 | 6 = 6): Promise<PX6Response<{ list: string[] }>> {
    return this.request('getcountry', { version })
  }

  // 获取代理列表
  async getProxies(
    state: 'active' | 'expired' | 'expiring' | 'all' = 'all',
    descr?: string,
    nokey?: boolean,
    page: number = 1,
    limit: number = 1000
  ): Promise<PX6Response<{ list_count: number; list: Record<string, ProxyInfo> }>> {
    const params: any = { state, page, limit }
    if (descr) params.descr = descr
    if (nokey) params.nokey = ''
    return this.request('getproxy', params)
  }

  // 购买代理
  async buyProxy(
    count: number,
    period: number,
    country: string,
    version: 4 | 3 | 6 = 6,
    type: 'http' | 'socks' = 'http',
    descr?: string,
    auto_prolong?: boolean,
    nokey?: boolean
  ): Promise<PX6Response<{ count: number; price: number; period: number; country: string; list: Record<string, ProxyInfo> }>> {
    const params: any = { count, period, country, version, type }
    if (descr) params.descr = descr
    if (auto_prolong) params.auto_prolong = ''
    if (nokey) params.nokey = ''
    return this.request('buy', params)
  }

  // 延长代理
  async prolongProxy(
    period: number,
    ids: string[],
    nokey?: boolean
  ): Promise<PX6Response<{ price: number; period: number; count: number; list: Record<string, { id: number; date_end: string; unixtime_end: number }> }>> {
    const params: any = { period, ids: ids.join(',') }
    if (nokey) params.nokey = ''
    return this.request('prolong', params)
  }

  // 删除代理
  async deleteProxy(ids?: string[], descr?: string): Promise<PX6Response<{ count: number }>> {
    if (!ids && !descr) {
      throw new Error('Either ids or descr must be provided')
    }
    const params: any = {}
    if (ids) params.ids = ids.join(',')
    if (descr) params.descr = descr
    return this.request('delete', params)
  }

  // 检查代理状态
  async checkProxy(id: string): Promise<PX6Response<{ proxy_id: number; proxy_status: boolean }>> {
    return this.request('check', { ids: id })
  }

  // 设置代理类型
  async setProxyType(ids: string[], type: 'http' | 'socks'): Promise<PX6Response> {
    return this.request('settype', { ids: ids.join(','), type })
  }

  // 设置代理描述
  async setProxyDescription(newDescr: string, oldDescr?: string, ids?: string[]): Promise<PX6Response<{ count: number }>> {
    if (!oldDescr && !ids) {
      throw new Error('Either oldDescr or ids must be provided')
    }
    const params: any = { new: newDescr }
    if (oldDescr) params.old = oldDescr
    if (ids) params.ids = ids.join(',')
    return this.request('setdescr', params)
  }

  // 获取账户信息
  async getAccountInfo(): Promise<PX6Response> {
    return this.request('')
  }
}

export const px6Service = new PX6Service()
