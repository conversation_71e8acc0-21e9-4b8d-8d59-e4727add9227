// 从Prisma导入的类型
export type {
  User,
  Account,
  Session,
  VerificationToken,
  Proxy,
  Order,
  Transaction,
  SystemConfig,
  Role,
  ProxyType,
  ProxyVersion,
  ProxyStatus,
  OrderStatus,
  TransactionType,
  TransactionStatus,
} from '@prisma/client'

// 扩展的用户类型
export interface UserWithStats extends User {
  _count: {
    orders: number
    proxies: number
    transactions: number
  }
}

// 订单创建类型
export interface CreateOrderData {
  count: number
  period: number
  country: string
  version: ProxyVersion
  type: ProxyType
  description?: string
}

// 代理购买请求类型
export interface BuyProxyRequest {
  count: number
  period: number
  country: string
  version: 'IPV4' | 'IPV4_SHARED' | 'IPV6'
  type: 'HTTP' | 'SOCKS'
  description?: string
}

// 代理延长请求类型
export interface ProlongProxyRequest {
  proxyIds: string[]
  period: number
}

// 充值请求类型
export interface RechargeRequest {
  amount: number
  paymentMethod: string
}

// 分页参数类型
export interface PaginationParams {
  page: number
  limit: number
}

// 分页响应类型
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 统计数据类型
export interface DashboardStats {
  totalUsers: number
  totalOrders: number
  totalProxies: number
  totalRevenue: number
  activeProxies: number
  expiringProxies: number
  recentOrders: Order[]
  recentTransactions: Transaction[]
}

// 国家信息类型
export interface CountryInfo {
  code: string
  name: string
  flag: string
  available: number
}

// 价格计算结果类型
export interface PriceCalculation {
  unitPrice: number
  totalPrice: number
  discount?: number
  discountAmount?: number
}

// 代理过滤参数类型
export interface ProxyFilters {
  status?: ProxyStatus
  country?: string
  type?: ProxyType
  version?: ProxyVersion
  search?: string
}

// 订单过滤参数类型
export interface OrderFilters {
  status?: OrderStatus
  dateFrom?: Date
  dateTo?: Date
  search?: string
}

// 交易过滤参数类型
export interface TransactionFilters {
  type?: TransactionType
  status?: TransactionStatus
  dateFrom?: Date
  dateTo?: Date
  search?: string
}
