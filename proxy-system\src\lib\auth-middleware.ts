import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from './auth'
import { ApiErrors } from './api-response'

export async function requireAuth(request: NextRequest) {
  const session = await getServerSession(authOptions)
  
  if (!session || !session.user) {
    throw ApiErrors.UNAUTHORIZED
  }
  
  return session
}

export async function requireAdmin(request: NextRequest) {
  const session = await requireAuth(request)
  
  if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPER_ADMIN') {
    throw ApiErrors.FORBIDDEN
  }
  
  return session
}

export async function requireSuperAdmin(request: NextRequest) {
  const session = await requireAuth(request)
  
  if (session.user.role !== 'SUPER_ADMIN') {
    throw ApiErrors.FORBIDDEN
  }
  
  return session
}
