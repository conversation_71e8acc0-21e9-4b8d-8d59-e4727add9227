import { NextResponse } from 'next/server'

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: string
}

export class ApiError extends Error {
  public statusCode: number
  public code?: string

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message)
    this.statusCode = statusCode
    this.code = code
    this.name = 'ApiError'
  }
}

export function createSuccessResponse<T>(data: T, message?: string): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    data,
    message,
  })
}

export function createErrorResponse(
  error: string | Error,
  statusCode: number = 500,
  code?: string
): NextResponse<ApiResponse> {
  const message = error instanceof Error ? error.message : error
  
  return NextResponse.json(
    {
      success: false,
      error: message,
      code,
    },
    { status: statusCode }
  )
}

export function handleApiError(error: unknown): NextResponse<ApiResponse> {
  console.error('API Error:', error)

  if (error instanceof ApiError) {
    return createErrorResponse(error.message, error.statusCode, error.code)
  }

  if (error instanceof Error) {
    return createErrorResponse(error.message, 500)
  }

  return createErrorResponse('Internal server error', 500)
}

// 常用错误
export const ApiErrors = {
  UNAUTHORIZED: new ApiError('未授权访问', 401, 'UNAUTHORIZED'),
  FORBIDDEN: new ApiError('禁止访问', 403, 'FORBIDDEN'),
  NOT_FOUND: new ApiError('资源不存在', 404, 'NOT_FOUND'),
  VALIDATION_ERROR: new ApiError('数据验证失败', 400, 'VALIDATION_ERROR'),
  INSUFFICIENT_BALANCE: new ApiError('余额不足', 400, 'INSUFFICIENT_BALANCE'),
  PROXY_NOT_AVAILABLE: new ApiError('代理不可用', 400, 'PROXY_NOT_AVAILABLE'),
  ORDER_NOT_FOUND: new ApiError('订单不存在', 404, 'ORDER_NOT_FOUND'),
  PAYMENT_FAILED: new ApiError('支付失败', 400, 'PAYMENT_FAILED'),
  PX6_API_ERROR: new ApiError('PX6 API调用失败', 500, 'PX6_API_ERROR'),
}
